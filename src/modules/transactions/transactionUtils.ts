import { Transaction } from "@/prisma/generated";

export interface TransactionGroup {
  groupId: string;
  transactions: Transaction[];
  totalTransactions: number;
  hasTransfer: boolean;
  hasTrades: boolean;
  executedAt: Date;
  accountName: string;
}

/**
 * Groups transactions by their transactionGroupId
 */
export function groupTransactionsByGroupId(transactions: any[]): TransactionGroup[] {
  const groups = new Map<string, any[]>();
  const ungroupedTransactions: any[] = [];

  // Separate grouped and ungrouped transactions
  for (const transaction of transactions) {
    if (transaction.transactionGroupId) {
      const groupId = transaction.transactionGroupId;
      if (!groups.has(groupId)) {
        groups.set(groupId, []);
      }
      groups.get(groupId)!.push(transaction);
    } else {
      ungroupedTransactions.push(transaction);
    }
  }

  const result: TransactionGroup[] = [];

  // Process grouped transactions
  for (const [groupId, groupTransactions] of groups.entries()) {
    const hasTransfer = groupTransactions.some(tx => tx.transfer);
    const hasTrades = groupTransactions.some(tx => tx.trade);
    const earliestTransaction = groupTransactions.reduce((earliest, current) => 
      current.executedAt < earliest.executedAt ? current : earliest
    );

    result.push({
      groupId,
      transactions: groupTransactions,
      totalTransactions: groupTransactions.length,
      hasTransfer,
      hasTrades,
      executedAt: earliestTransaction.executedAt,
      accountName: earliestTransaction.account?.name || 'Unknown Account',
    });
  }

  // Process ungrouped transactions (each becomes its own "group")
  for (const transaction of ungroupedTransactions) {
    result.push({
      groupId: transaction.id, // Use transaction ID as group ID for ungrouped
      transactions: [transaction],
      totalTransactions: 1,
      hasTransfer: !!transaction.transfer,
      hasTrades: !!transaction.trade,
      executedAt: transaction.executedAt,
      accountName: transaction.account?.name || 'Unknown Account',
    });
  }

  // Sort by execution date (newest first)
  return result.sort((a, b) => b.executedAt.getTime() - a.executedAt.getTime());
}

/**
 * Gets a human-readable description for a transaction group
 */
export function getTransactionGroupDescription(group: TransactionGroup): string {
  if (group.totalTransactions === 1) {
    const transaction = group.transactions[0];
    if (transaction.transfer) {
      return `Transfer: ${transaction.transfer.description || 'No description'}`;
    }
    if (transaction.trade) {
      return `Trade: ${transaction.trade.tokenFrom} → ${transaction.trade.tokenTo}`;
    }
    return 'Unknown transaction type';
  }

  // Multiple transactions in group
  const parts: string[] = [];
  if (group.hasTransfer) {
    parts.push('Transfer');
  }
  if (group.hasTrades) {
    const tradeCount = group.transactions.filter(tx => tx.trade).length;
    parts.push(`${tradeCount} Trade${tradeCount > 1 ? 's' : ''}`);
  }

  return parts.join(' + ');
}

/**
 * Gets the total amount for a transaction group (from transfers only)
 */
export function getTransactionGroupAmount(group: TransactionGroup): { amount: number; currency: string } | null {
  const transferTransaction = group.transactions.find(tx => tx.transfer);
  if (transferTransaction?.transfer) {
    return {
      amount: Number(transferTransaction.transfer.amount),
      currency: transferTransaction.transfer.currencyCode,
    };
  }
  return null;
}
