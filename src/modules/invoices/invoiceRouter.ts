import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { Decimal } from "@/prisma/generated/runtime/library";
import { ReconciliationStatus } from "@/prisma/generated";
import { monetary } from "@/modules/core/monetary";
import { getAccountingYearRange } from "@/modules/settings/settingsUtils";

export const invoiceRouter = createTRPCRouter({
  getAll: publicProcedure
    .input(
      z
        .object({
          limit: z.number().min(1).max(100).default(10),
          cursor: z.string().optional(),
          currencyCodes: z.array(z.string()).optional(),
          year: z.number().optional(),
          search: z.string().optional(),
          reconciliationStatus: z.nativeEnum(ReconciliationStatus).optional(),
          dateFrom: z.string().optional(),
          dateTo: z.string().optional(),
          amountMin: z.number().optional(),
          amountMax: z.number().optional(),
          vendorId: z.string().optional(),
          sortField: z.enum(["date", "vendor", "amount"]).optional(),
          sortDirection: z.enum(["asc", "desc"]).optional(),
        })
        .optional()
        .default({})
    )
    .query(async ({ input, ctx }) => {
      try {
        const limit = input?.limit ?? 10;
        const cursor = input?.cursor;

        // Build where clause
        let whereClause: any = {};

        // Currency filter
        if (input?.currencyCodes?.length) {
          whereClause.currencyCode = { in: input.currencyCodes };
        }

        // Year filter
        if (input?.year) {
          // Get accounting year settings
          const settings = await ctx.prisma.settings.findFirst();
          if (settings?.accountingYearStartDay && settings?.accountingYearStartMonth) {
            const yearRange = getAccountingYearRange(input.year, {
              day: settings.accountingYearStartDay,
              month: settings.accountingYearStartMonth,
            });
            whereClause.date = {
              gte: yearRange.start,
              lte: yearRange.end,
            };
          } else {
            // Fallback to calendar year if no accounting year settings
            whereClause.date = {
              gte: new Date(input.year, 0, 1), // January 1st
              lte: new Date(input.year, 11, 31), // December 31st
            };
          }
        }

        // Search filter
        if (input?.search && input.search.trim()) {
          const searchTerm = input.search.trim();
          whereClause.OR = [
            // Search in import item text
            {
              importItem: {
                text: {
                  search: searchTerm,
                },
              },
            },
            // Search in vendor fields
            {
              vendor: {
                name: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                country: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                vatNumber: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                email: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                contactPerson: {
                  search: searchTerm,
                },
              },
            },
          ];
        }

        // Reconciliation status filter
        if (input?.reconciliationStatus) {
          if (input.reconciliationStatus === ReconciliationStatus.UNMATCHED) {
            // For unmatched, we want invoices with no reconciliations
            whereClause.reconciliations = {
              none: {},
            };
          } else {
            // For matched statuses, we want invoices with reconciliations of that status
            whereClause.reconciliations = {
              some: {
                status: input.reconciliationStatus,
              },
            };
          }
        }

        // Date range filter
        if (input?.dateFrom || input?.dateTo) {
          whereClause.date = {};
          if (input.dateFrom) {
            whereClause.date.gte = new Date(input.dateFrom);
          }
          if (input.dateTo) {
            whereClause.date.lte = new Date(input.dateTo);
          }
        }

        // Amount range filter
        if (input?.amountMin !== undefined || input?.amountMax !== undefined) {
          whereClause.amountGross = {};
          if (input.amountMin !== undefined) {
            whereClause.amountGross.gte = input.amountMin;
          }
          if (input.amountMax !== undefined) {
            whereClause.amountGross.lte = input.amountMax;
          }
        }

        // Vendor filter
        if (input?.vendorId) {
          whereClause.vendorId = input.vendorId;
        }

        // Build orderBy clause
        let orderBy: any = { date: "desc" }; // Default sort
        if (input?.sortField && input?.sortDirection) {
          switch (input.sortField) {
            case "date":
              orderBy = { date: input.sortDirection };
              break;
            case "vendor":
              orderBy = { vendor: { name: input.sortDirection } };
              break;
            case "amount":
              orderBy = { amountGross: input.sortDirection };
              break;
          }
        }

        const invoices = await ctx.prisma.invoice.findMany({
          take: limit + 1, // Take one extra to check if there's a next page
          cursor: cursor ? { id: cursor } : undefined,
          orderBy,
          where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
          include: {
            vendor: true,
            importItem: {
              select: {
                id: true,
                externalId: true,
                fileUrl: true,
                createdAt: true,
                importItemType: true,
                text: true,
              },
            },
            reconciliations: {
              where: {
                status: {
                  in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
                },
              },
              select: {
                id: true,
                status: true,
                confidenceScore: true,
                transfer: {
                  select: {
                    id: true,
                    amount: true,
                    currencyCode: true,
                    transaction: {
                      select: {
                        executedAt: true,
                        account: {
                          select: {
                            name: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        });

        let nextCursor: string | undefined = undefined;
        if (invoices.length > limit) {
          const nextItem = invoices.pop(); // Remove the extra item
          nextCursor = nextItem!.id;
        }

        return {
          items: invoices,
          nextCursor,
        };
      } catch (error) {
        console.error("Failed to fetch invoices:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch invoices",
          cause: error,
        });
      }
    }),

  getById: publicProcedure.input(z.object({ id: z.string() })).query(async ({ input, ctx }) => {
    try {
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.id },
        include: {
          importItem: {
            select: {
              id: true,
              externalId: true,
              fileUrl: true,
              createdAt: true,
              importItemType: true,
              text: true,
            },
          },
          lineItems: {
            orderBy: { id: "asc" },
          },
          vendor: true,
        },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      return invoice;
    } catch (error) {
      console.error("Failed to fetch invoice:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch invoice",
        cause: error,
      });
    }
  }),

  getStats: publicProcedure.input(z.object({}).optional().default({})).query(async ({ ctx }) => {
    try {
      const [total, withVAT, reverseCharge] = await Promise.all([
        ctx.prisma.invoice.count(),
        ctx.prisma.invoice.count({ where: { hasVAT: true } }),
        ctx.prisma.invoice.count({ where: { isReverseCharge: true } }),
      ]);

      const amountStats = await ctx.prisma.invoice.aggregate({
        _sum: {
          amountGross: true,
          amountNet: true,
          amountVat: true,
        },
      });

      // Calculate average amounts
      // const amountStats = await ctx.prisma.invoice.aggregate({
      //   _avg: {
      //     totalAmountGross: true,
      //     totalAmountNet: true,
      //   },
      //   _sum: {
      //     totalAmountGross: true,
      //     totalAmountNet: true,
      //   },
      // });

      return {
        total,
        withVAT,
        reverseCharge,
        withoutVAT: total - withVAT,
        totalAmountGross: amountStats._sum?.amountGross,
        totalAmountNet: amountStats._sum?.amountNet,
        totalAmountVat: amountStats._sum?.amountVat,
      };
    } catch (error) {
      console.error("Failed to fetch invoice stats:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch invoice stats",
        cause: error,
      });
    }
  }),

  getFilteredStats: publicProcedure
    .input(
      z
        .object({
          currencyCodes: z.array(z.string()).optional(),
          year: z.number().optional(),
          search: z.string().optional(),
          reconciliationStatus: z.nativeEnum(ReconciliationStatus).optional(),
          dateFrom: z.string().optional(),
          dateTo: z.string().optional(),
          amountMin: z.number().optional(),
          amountMax: z.number().optional(),
          vendorId: z.string().optional(),
        })
        .optional()
        .default({})
    )
    .query(async ({ input, ctx }) => {
      try {
        // Build where clause - reuse the same logic as getAll
        let whereClause: any = {};

        // Currency filter
        if (input?.currencyCodes?.length) {
          whereClause.currencyCode = { in: input.currencyCodes };
        }

        // Year filter
        if (input?.year) {
          // Get accounting year settings
          const settings = await ctx.prisma.settings.findFirst();
          if (settings?.accountingYearStartDay && settings?.accountingYearStartMonth) {
            const yearRange = getAccountingYearRange(input.year, {
              day: settings.accountingYearStartDay,
              month: settings.accountingYearStartMonth,
            });
            whereClause.date = {
              gte: yearRange.start,
              lte: yearRange.end,
            };
          } else {
            // Fallback to calendar year if no accounting year settings
            whereClause.date = {
              gte: new Date(input.year, 0, 1), // January 1st
              lte: new Date(input.year, 11, 31), // December 31st
            };
          }
        }

        // Search filter
        if (input?.search && input.search.trim()) {
          const searchTerm = input.search.trim();
          whereClause.OR = [
            // Search in import item text
            {
              importItem: {
                text: {
                  search: searchTerm,
                },
              },
            },
            // Search in vendor fields
            {
              vendor: {
                name: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                country: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                vatNumber: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                email: {
                  search: searchTerm,
                },
              },
            },
            {
              vendor: {
                contactPerson: {
                  search: searchTerm,
                },
              },
            },
          ];
        }

        // Reconciliation status filter
        if (input?.reconciliationStatus) {
          if (input.reconciliationStatus === ReconciliationStatus.UNMATCHED) {
            // For unmatched, we want invoices with no reconciliations
            whereClause.reconciliations = {
              none: {},
            };
          } else {
            // For matched statuses, we want invoices with reconciliations of that status
            whereClause.reconciliations = {
              some: {
                status: input.reconciliationStatus,
              },
            };
          }
        }

        // Date range filter
        if (input?.dateFrom || input?.dateTo) {
          whereClause.date = {};
          if (input.dateFrom) {
            whereClause.date.gte = new Date(input.dateFrom);
          }
          if (input.dateTo) {
            whereClause.date.lte = new Date(input.dateTo);
          }
        }

        // Amount range filter
        if (input?.amountMin !== undefined || input?.amountMax !== undefined) {
          whereClause.amountGross = {};
          if (input.amountMin !== undefined) {
            whereClause.amountGross.gte = input.amountMin;
          }
          if (input.amountMax !== undefined) {
            whereClause.amountGross.lte = input.amountMax;
          }
        }

        // Vendor filter
        if (input?.vendorId) {
          whereClause.vendorId = input.vendorId;
        }

        const [total, withVAT] = await Promise.all([
          ctx.prisma.invoice.count({ where: Object.keys(whereClause).length > 0 ? whereClause : undefined }),
          ctx.prisma.invoice.count({ where: { ...whereClause, hasVAT: true } }),
        ]);

        const amountStats = await ctx.prisma.invoice.aggregate({
          _sum: {
            amountGross: true,
            amountVat: true,
          },
          where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
        });

        return {
          total,
          withVAT,
          withoutVAT: total - withVAT,
          totalAmountGross: amountStats._sum?.amountGross,
          totalAmountVat: amountStats._sum?.amountVat,
        };
      } catch (error) {
        console.error("Failed to fetch filtered invoice stats:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch filtered invoice stats",
          cause: error,
        });
      }
    }),

  updateLineItems: publicProcedure
    .input(
      z.object({
        invoiceId: z.string(),
        lineItems: z.array(
          z.object({
            id: z.string().optional(), // Optional for new items
            name: z.string().min(1, "Item name is required"),
            description: z.string().optional(),
            quantity: z.number().int().min(1).default(1),
            priceGross: z.number().min(0, "Price must be positive"),
            priceNet: z.number().min(0).optional(),
            vatRate: z.number().min(0).max(100).optional(),
          })
        ),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const { invoiceId, lineItems } = input;

        // Verify invoice exists
        const existingInvoice = await ctx.prisma.invoice.findUnique({
          where: { id: invoiceId },
          include: { lineItems: true },
        });

        if (!existingInvoice) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Invoice not found",
          });
        }

        // Calculate totals from line items
        let totalAmountGross = new Decimal(0);
        let totalAmountNet = new Decimal(0);
        let totalAmountVat = new Decimal(0);

        const processedLineItems = lineItems.map((item) => {
          const quantity = item.quantity || 1;
          const itemTotalGross = monetary.toDecimal(item.priceGross).mul(quantity);

          let itemTotalNet = new Decimal(0);
          let itemVatAmount = new Decimal(0);

          if (item.priceNet) {
            itemTotalNet = monetary.toDecimal(item.priceNet).mul(quantity);
            itemVatAmount = itemTotalGross.sub(itemTotalNet);
          } else if (item.vatRate) {
            // Calculate net from gross using VAT rate
            const vatMultiplier = new Decimal(1).add(monetary.roundVatRate(item.vatRate).div(100));
            itemTotalNet = itemTotalGross.div(vatMultiplier);
            itemVatAmount = itemTotalGross.sub(itemTotalNet);
          } else {
            // No VAT information, assume gross = net
            itemTotalNet = itemTotalGross;
          }

          totalAmountGross = totalAmountGross.add(itemTotalGross);
          totalAmountNet = totalAmountNet.add(itemTotalNet);
          totalAmountVat = totalAmountVat.add(itemVatAmount);

          return {
            ...item,
            ...(!item.vatRate ? { vatRate: null, priceNet: null } : {}),
            priceNet: monetary.toNumber(itemTotalNet.div(quantity)),
          };
        });

        // Update invoice in a transaction
        const updatedInvoice = await ctx.prisma.$transaction(async (tx) => {
          // Delete existing line items
          await tx.invoiceLineItem.deleteMany({
            where: { invoiceId },
          });

          // Create new line items
          await tx.invoiceLineItem.createMany({
            data: processedLineItems.map((item) => ({
              invoiceId,
              name: item.name,
              description: item.description,
              quantity: item.quantity,
              priceGross: monetary.forDatabase(item.priceGross),
              priceNet: monetary.forDatabase(item.priceNet || 0),
              vatRate: monetary.roundVatRate(item.vatRate ?? 0),
              vatAmount: monetary.forDatabase((item.priceGross || 0) - (item.priceNet || 0)),
            })) as any,
          });

          // Update invoice totals
          return await tx.invoice.update({
            where: { id: invoiceId },
            data: {
              amountGross: totalAmountGross,
              amountNet: totalAmountNet,
              amountVat: totalAmountVat,
              hasVAT: totalAmountVat.gt(0),
            },
            include: {
              importItem: {
                select: {
                  id: true,
                  externalId: true,
                  fileUrl: true,
                  createdAt: true,
                  importItemType: true,
                  text: true,
                },
              },
              lineItems: {
                orderBy: { id: "asc" },
              },
              vendor: true,
            },
          });
        });

        return updatedInvoice;
      } catch (error) {
        console.error("Failed to update invoice line items:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update invoice line items",
          cause: error,
        });
      }
    }),

  validateInvoice: publicProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    try {
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.id },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      if (invoice.validatedAt) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invoice is already validated",
        });
      }

      const updatedInvoice = await ctx.prisma.invoice.update({
        where: { id: input.id },
        data: {
          validatedAt: new Date(),
        },
        include: {
          importItem: {
            select: {
              id: true,
              externalId: true,
              fileUrl: true,
              createdAt: true,
              importItemType: true,
              text: true,
            },
          },
          lineItems: {
            orderBy: { id: "asc" },
          },
          vendor: true,
        },
      });

      return updatedInvoice;
    } catch (error) {
      console.error("Failed to validate invoice:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to validate invoice",
        cause: error,
      });
    }
  }),

  unvalidateInvoice: publicProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    try {
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.id },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      if (!invoice.validatedAt) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invoice is not validated",
        });
      }

      const updatedInvoice = await ctx.prisma.invoice.update({
        where: { id: input.id },
        data: {
          validatedAt: null,
        },
        include: {
          importItem: {
            select: {
              id: true,
              externalId: true,
              fileUrl: true,
              createdAt: true,
              importItemType: true,
              text: true,
            },
          },
          lineItems: {
            orderBy: { id: "asc" },
          },
          vendor: true,
        },
      });

      return updatedInvoice;
    } catch (error) {
      console.error("Failed to unvalidate invoice:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to unvalidate invoice",
        cause: error,
      });
    }
  }),

  delete: publicProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    try {
      // Check if invoice exists
      const existingInvoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.id },
        include: {
          reconciliations: true,
          lineItems: true,
        },
      });

      if (!existingInvoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      // Delete the invoice - related records will be cascade deleted automatically
      // (reconciliations and lineItems have onDelete: Cascade)
      await ctx.prisma.invoice.delete({
        where: { id: input.id },
      });

      return { success: true };
    } catch (error) {
      console.error("Failed to delete invoice:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete invoice",
        cause: error,
      });
    }
  }),

  bulkDelete: publicProcedure
    .input(
      z.object({
        currencyCodes: z.array(z.string()).optional(),
        year: z.number().optional(),
        search: z.string().optional(),
        reconciliationStatus: z.nativeEnum(ReconciliationStatus).optional(),
        dateFrom: z.string().optional(),
        dateTo: z.string().optional(),
        amountMin: z.number().optional(),
        amountMax: z.number().optional(),
        vendorId: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Build where clause - reuse the same logic as getAll
        let whereClause: any = {};

        // Currency filter
        if (input?.currencyCodes?.length) {
          whereClause.currencyCode = { in: input.currencyCodes };
        }

        // Year filter
        if (input?.year) {
          const { start, end } = await getAccountingYearRange(input.year, {
            day: 1,
            month: 11,
          });
          whereClause.date = {
            gte: start,
            lte: end,
          };
        }

        // Search filter
        if (input?.search?.trim()) {
          const searchTerm = input.search.trim();
          whereClause.OR = [
            {
              vendor: {
                name: {
                  search: searchTerm,
                },
              },
            },
            {
              invoiceReference: {
                search: searchTerm,
              },
            },
            {
              importItem: {
                text: {
                  search: searchTerm,
                },
              },
            },
          ];
        }

        // Reconciliation status filter
        if (input?.reconciliationStatus) {
          if (input.reconciliationStatus === ReconciliationStatus.UNMATCHED) {
            // For unmatched, we want invoices with no reconciliations
            whereClause.reconciliations = {
              none: {},
            };
          } else {
            // For matched statuses, we want invoices with reconciliations of that status
            whereClause.reconciliations = {
              some: {
                status: input.reconciliationStatus,
              },
            };
          }
        }

        // Date range filter
        if (input?.dateFrom || input?.dateTo) {
          whereClause.date = {};
          if (input.dateFrom) {
            whereClause.date.gte = new Date(input.dateFrom);
          }
          if (input.dateTo) {
            whereClause.date.lte = new Date(input.dateTo);
          }
        }

        // Amount range filter
        if (input?.amountMin !== undefined || input?.amountMax !== undefined) {
          whereClause.amountGross = {};
          if (input.amountMin !== undefined) {
            whereClause.amountGross.gte = input.amountMin;
          }
          if (input.amountMax !== undefined) {
            whereClause.amountGross.lte = input.amountMax;
          }
        }

        // Vendor filter
        if (input?.vendorId) {
          whereClause.vendorId = input.vendorId;
        }

        // First, get the count of invoices that will be deleted
        const count = await ctx.prisma.invoice.count({
          where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
        });

        if (count === 0) {
          return { deletedCount: 0 };
        }

        // Delete the invoices - related records will be cascade deleted automatically
        // (reconciliations and lineItems have onDelete: Cascade)
        const result = await ctx.prisma.invoice.deleteMany({
          where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
        });

        return { deletedCount: result.count };
      } catch (error) {
        console.error("Failed to bulk delete invoices:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to bulk delete invoices",
          cause: error,
        });
      }
    }),

  /**
   * Return the list of distinct currency codes present on invoices.
   */
  getCurrencies: publicProcedure.query(async ({ ctx }) => {
    try {
      const currencies = await ctx.prisma.invoice.findMany({
        distinct: ["currencyCode"],
        select: { currencyCode: true },
        orderBy: { currencyCode: "asc" },
      });

      return currencies.map((c) => c.currencyCode).filter((c): c is string => Boolean(c));
    } catch (error) {
      console.error("Failed to fetch currencies:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch currencies",
        cause: error,
      });
    }
  }),

  /**
   * Return the list of available years based on invoice dates and accounting year settings.
   */
  getAvailableYears: publicProcedure.query(async ({ ctx }) => {
    try {
      // Get the earliest and latest invoice dates
      const dateRange = await ctx.prisma.invoice.aggregate({
        _min: { date: true },
        _max: { date: true },
      });

      if (!dateRange._min?.date || !dateRange._max?.date) {
        return [];
      }

      // Get accounting year settings
      const settings = await ctx.prisma.settings.findFirst();

      const years: number[] = [];

      if (settings?.accountingYearStartDay && settings?.accountingYearStartMonth) {
        // Use accounting years
        const { getAccountingYearForDate } = await import("@/modules/settings/settingsUtils");

        const startYear = getAccountingYearForDate(dateRange._min.date, {
          day: settings.accountingYearStartDay,
          month: settings.accountingYearStartMonth,
        });

        const endYear = getAccountingYearForDate(dateRange._max.date, {
          day: settings.accountingYearStartDay,
          month: settings.accountingYearStartMonth,
        });

        for (let year = startYear; year <= endYear; year++) {
          years.push(year);
        }
      } else {
        // Fallback to calendar years
        const startYear = dateRange._min.date.getFullYear();
        const endYear = dateRange._max.date.getFullYear();

        for (let year = startYear; year <= endYear; year++) {
          years.push(year);
        }
      }

      return years.sort((a, b) => b - a); // Sort descending (newest first)
    } catch (error) {
      console.error("Failed to fetch available years:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch available years",
        cause: error,
      });
    }
  }),
});
