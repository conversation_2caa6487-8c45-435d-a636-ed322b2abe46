import <PERSON> from "papaparse";
import { Result, ok, err } from "neverthrow";
import { Decimal } from "@/prisma/generated/runtime/library";
import { z } from "zod";
import { llm } from "@/modules/ai/llm/llm";
import { motlLlm } from "@/modules/ai/llm/motlLlm";
import { parse } from "date-fns";
import { zodUtils } from "./zodUtils";

export namespace csvUtils {
  export interface ParsedCSV {
    data: Record<string, any>[];
    headers: string[];
    rowCount: number;
    preview: Record<string, any>[];
  }

  export function parseCSV(csvContent: string): Result<ParsedCSV, { type: "CSVParseError"; message: string; error: any }> {
    try {
      const parseResult = Papa.parse(csvContent, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim(),
        transform: (value: string) => value.trim(),
      });

      if (parseResult.errors.length > 0) {
        return err({
          type: "CSVParseError",
          message: `CSV parsing errors: ${parseResult.errors.map((e) => e.message).join(", ")}`,
          error: parseResult.errors,
        });
      }

      const data = parseResult.data as Record<string, any>[];
      const headers = parseResult.meta.fields || [];
      const preview = data.slice(0, 5); // First 5 rows for preview

      return ok({
        data,
        headers,
        rowCount: data.length,
        preview,
      });
    } catch (error) {
      return err({
        type: "CSVParseError",
        message: error instanceof Error ? error.message : "Unknown CSV parsing error",
        error,
      });
    }
  }

  export function analyzeCSVStructure(parsed: ParsedCSV): {
    summary: string;
    columnTypes: Record<string, string>;
    sampleData: string;
  } {
    const { data, headers } = parsed;

    // Analyze column types
    const columnTypes: Record<string, string> = {};
    headers.forEach((header) => {
      const sampleValues = data
        .slice(0, 10)
        .map((row) => row[header])
        .filter((val) => val !== null && val !== undefined && val !== "");

      if (sampleValues.length === 0) {
        columnTypes[header] = "empty";
        return;
      }

      // Check if all values are numbers
      const allNumbers = sampleValues.every((val) => !isNaN(Number(val)) && val !== "");
      if (allNumbers) {
        columnTypes[header] = "number";
        return;
      }

      // Check if all values are dates
      const allDates = sampleValues.every((val) => !isNaN(Date.parse(val)));
      if (allDates) {
        columnTypes[header] = "date";
        return;
      }

      columnTypes[header] = "text";
    });

    const summary = `CSV contains ${parsed.rowCount} rows and ${headers.length} columns: ${headers.join(", ")}`;
    const sampleData = JSON.stringify(parsed.preview, null, 2);

    return {
      summary,
      columnTypes,
      sampleData,
    };
  }

  // Binance-specific CSV processing
  export interface BinanceTransaction {
    timestamp: Date;
    description: string;
    paidOut?: number;
    paidIn?: number;
    transactionFee: number;
    assetsUsed: string;
    exchangeRates: string;
  }

  export interface ParsedAsset {
    currency: string;
    amount: number;
  }

  export interface ParsedExchangeRate {
    fromCurrency: string;
    toCurrency: string;
    rate: number;
  }

  export interface ProcessedTransaction {
    transfer: {
      from: string;
      to: string;
      amount: Decimal;
      currencyCode: string;
      description: string;
      executedAt: Date;
    };
    trades: {
      tokenFrom: string;
      tokenTo: string;
      amountFrom: Decimal;
      amountTo: Decimal;
      description: string;
      poolId: string;
      executedAt: Date;
    }[];
  }

  export function parseBinanceCSV(csvContent: string): Result<BinanceTransaction[], { type: "BinanceParseError"; message: string; error: any }> {
    const parseResult = parseCSV(csvContent);
    if (parseResult.isErr()) {
      return err({
        type: "BinanceParseError",
        message: parseResult.error.message,
        error: parseResult.error,
      });
    }

    try {
      const transactions: BinanceTransaction[] = parseResult.value.data.map((row) => {
        const timestamp = new Date(row.Timestamp);
        if (isNaN(timestamp.getTime())) {
          throw new Error(`Invalid timestamp: ${row.Timestamp}`);
        }

        return {
          timestamp,
          description: row.Description || "",
          paidOut: row["Paid OUT (EUR)"] ? parseFloat(row["Paid OUT (EUR)"]) : undefined,
          paidIn: row["Paid IN (EUR)"] ? parseFloat(row["Paid IN (EUR)"]) : undefined,
          transactionFee: parseFloat(row["Transaction Fee (EUR)"]) || 0,
          assetsUsed: row["Assets Used"] || "",
          exchangeRates: row["Exchange Rates"] || "",
        };
      });

      return ok(transactions);
    } catch (error) {
      return err({
        type: "BinanceParseError",
        message: error instanceof Error ? error.message : "Unknown Binance parsing error",
        error,
      });
    }
  }

  export async function parseGenericCSV(csvContent: string) {
    const columnMapping = z.object({
      columnMappings: z.object({
        date: z.string(),
        amount: z.string(),
        currencyCode: z.string(),
        counterparty: z.string(),
        description: zodUtils.optional(z.string()),
      }),
      dateFormat: z.string().describe("The date format of the csv. This should be in the correct format for the date-fns library parse function."),
      currencyCodeValueForAllRows: zodUtils
        .optional(z.string())
        .describe("The currency code value for all rows. This is used to determine the currency code for all rows."),
    });

    const parseResult = parseCSV(csvContent);

    if (parseResult.isErr()) {
      return err({
        type: "GenericParseError",
        message: parseResult.error.message,
        error: parseResult.error,
      });
    }

    const headers = parseResult.value.headers;
    const rows = parseResult.value.data;

    return motlLlm.generateObjectWithValidation({
      aiModel: "o4-mini",
      schema: columnMapping,
      messages: [
        {
          role: "system",
          content: `You are a helpful assistant that gets a csv file and must map the column names of the csv to the column names of our database. You must also map the date format of the csv to the date format of our database.
you also need to provide the date format of the csv.
            `,
        },
        {
          role: "user",
          content: csvContent,
        },
      ],
      validateAndTransform(data) {
        data.columnMappings;

        const mappedRows = rows
          .map((row) => {
            return Object.fromEntries(Object.entries(data.columnMappings).map(([key, value]) => [key, row[value]])) as z.infer<
              typeof columnMapping.shape.columnMappings
            >;
          })
          .map((row) => {
            const ts = parse(row.date, data.dateFormat, new Date());

            return {
              ...row,
              date: ts,
              currencyCode: data.currencyCodeValueForAllRows ? data.currencyCodeValueForAllRows : row.currencyCode,
            };
          });

        return ok({
          data: mappedRows,
        });
      },
    });
  }
}
