import { Flipside, Query, QueryResultSet } from "@flipsidecrypto/sdk";
import { Result, ResultAsync, ok, err } from "neverthrow";
import { envVars } from "@/envVars";

/**
 * Error types for Flipside operations
 */
export type FlipsideError =
  | { type: "FLIPSIDE_INIT_ERROR"; message: string; rawError: unknown }
  | { type: "FLIPSIDE_QUERY_ERROR"; message: string; rawError: unknown }
  | { type: "FLIPSIDE_TIMEOUT_ERROR"; message: string; queryId?: string; rawError: unknown }
  | { type: "FLIPSIDE_RATE_LIMIT_ERROR"; message: string; rawError: unknown }
  | { type: "FLIPSIDE_VALIDATION_ERROR"; message: string; details: string; rawError: unknown };

/**
 * Configuration options for Flipside queries
 */
export interface FlipsideQueryOptions {
  /** The number of minutes you are willing to accept cached results up to */
  maxAgeMinutes?: number;
  /** Override query result cache - false will re-execute the query */
  cached?: boolean;
  /** The number of minutes until your query run times out */
  timeoutMinutes?: number;
  /** The number of records to return, defaults to 100000 */
  pageSize?: number;
  /** The page number to return, defaults to 1 */
  pageNumber?: number;
  /** The owner of the data source (defaults to 'flipside') */
  dataProvider?: string;
  /** The data source to execute the query against (defaults to 'snowflake-default') */
  dataSource?: string;
}

/**
 * Flipside client wrapper with error handling using neverthrow
 */
export class FlipsideClient {
  private flipside: Flipside;

  constructor(apiKey?: string, apiUrl?: string) {
    try {
      const key = apiKey || envVars.FLIPSIDE_API_KEY;
      const url = apiUrl || "https://api-v2.flipsidecrypto.xyz";

      if (!key) {
        throw new Error("Flipside API key is required");
      }

      this.flipside = new Flipside(key, url);
    } catch (error) {
      throw new Error(`Failed to initialize Flipside client: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Execute a SQL query against Flipside's data warehouse
   */
  async query(sql: string, options: FlipsideQueryOptions = {}): Promise<Result<QueryResultSet, FlipsideError>> {
    return ResultAsync.fromPromise(this.executeQuery(sql, options), (error): FlipsideError => {
      console.error("Flipside query failed:", error);

      if (error && typeof error === "object" && "message" in error) {
        const errorMessage = String(error.message);

        if (errorMessage.includes("timeout")) {
          return {
            type: "FLIPSIDE_TIMEOUT_ERROR",
            message: "Query execution timed out",
            rawError: error,
          };
        }

        if (errorMessage.includes("rate limit")) {
          return {
            type: "FLIPSIDE_RATE_LIMIT_ERROR",
            message: "Rate limit exceeded",
            rawError: error,
          };
        }
      }

      return {
        type: "FLIPSIDE_QUERY_ERROR",
        message: error instanceof Error ? error.message : "Unknown query error",
        rawError: error,
      };
    });
  }

  /**
   * Get paginated results for a query
   */
  async getQueryResults(queryRunId: string, pageNumber: number = 1, pageSize: number = 1000): Promise<Result<QueryResultSet, FlipsideError>> {
    return ResultAsync.fromPromise(
      this.flipside.query.getQueryResults({
        queryRunId,
        pageNumber,
        pageSize,
      }),
      (error): FlipsideError => {
        console.error("Failed to get query results:", error);
        return {
          type: "FLIPSIDE_QUERY_ERROR",
          message: error instanceof Error ? error.message : "Failed to get query results",
          rawError: error,
        };
      }
    );
  }

  /**
   * Execute all pages of a query and return combined results
   */
  async queryAllPages(sql: string, options: FlipsideQueryOptions = {}): Promise<Result<QueryResultSet[], FlipsideError>> {
    const initialResult = await this.query(sql, { ...options, pageSize: 1000 });

    if (initialResult.isErr()) {
      return err(initialResult.error);
    }

    const firstPage = initialResult.value;
    if (!firstPage.queryId || !firstPage.page?.totalPages) {
      return ok([firstPage]);
    }

    const allPages: QueryResultSet[] = [firstPage];
    const totalPages = firstPage.page.totalPages;

    // Fetch remaining pages
    for (let pageNum = 2; pageNum <= totalPages; pageNum++) {
      const pageResult = await this.getQueryResults(firstPage.queryId, pageNum, 1000);

      if (pageResult.isErr()) {
        return err(pageResult.error);
      }

      allPages.push(pageResult.value);
    }

    return ok(allPages);
  }

  /**
   * Execute a SQL query and return the result directly (throws on error)
   * This is a convenience method for when you want simpler error handling
   */
  async queryDirect(sql: string, options: FlipsideQueryOptions = {}): Promise<QueryResultSet> {
    const result = await this.query(sql, options);

    if (result.isErr()) {
      throw new Error(`Flipside query failed: ${result.error.message}`);
    }

    return result.value;
  }

  /**
   * Execute a SQL query and return only the records directly (throws on error)
   * This is the most convenient method for simple data retrieval
   */
  async queryRecords<T = any>(sql: string, options: FlipsideQueryOptions = {}): Promise<T[]> {
    const resultSet = await this.queryDirect(sql, options);
    return FlipsideUtils.extractRecords<T>(resultSet);
  }

  /**
   * Execute all pages of a query and return combined records directly (throws on error)
   */
  async queryAllRecords<T = any>(sql: string, options: FlipsideQueryOptions = {}): Promise<T[]> {
    const pagesResult = await this.queryAllPages(sql, options);

    if (pagesResult.isErr()) {
      throw new Error(`Flipside query failed: ${pagesResult.error.message}`);
    }

    return FlipsideUtils.combinePageRecords<T>(pagesResult.value);
  }

  private async executeQuery(sql: string, options: FlipsideQueryOptions): Promise<QueryResultSet> {
    const query: Query = {
      sql,
      maxAgeMinutes: options.maxAgeMinutes ?? 30,
      cached: options.cached,
      timeoutMinutes: options.timeoutMinutes ?? 15,
      pageSize: options.pageSize ?? 100000,
      pageNumber: options.pageNumber ?? 1,
      dataProvider: options.dataProvider,
      dataSource: options.dataSource,
    };

    const result = await this.flipside.query.run(query);

    // Check for errors in the result
    if (result.error) {
      throw new Error(`Query execution failed: ${JSON.stringify(result.error)}`);
    }

    return result;
  }
}

/**
 * Default Flipside client instance
 */
export const flipsideClient = new FlipsideClient();

/**
 * Utility functions for common Flipside operations
 */
export namespace FlipsideUtils {
  /**
   * Format SQL query with proper escaping for string parameters
   */
  export function formatSql(template: string, params: Record<string, string | number>): string {
    let formatted = template;

    for (const [key, value] of Object.entries(params)) {
      const placeholder = `{${key}}`;
      const escapedValue = typeof value === "string" ? `'${value.replace(/'/g, "''")}'` : String(value);

      formatted = formatted.replace(new RegExp(placeholder, "g"), escapedValue);
    }

    return formatted;
  }

  /**
   * Extract records from query result set
   */
  export function extractRecords<T = any>(resultSet: QueryResultSet): T[] {
    return resultSet.records || [];
  }

  /**
   * Combine records from multiple pages
   */
  export function combinePageRecords<T = any>(pages: QueryResultSet[]): T[] {
    return pages.flatMap((page) => extractRecords<T>(page));
  }

  /**
   * Get query execution stats
   */
  export function getQueryStats(resultSet: QueryResultSet) {
    return {
      queryId: resultSet.queryId,
      status: resultSet.status,
      recordCount: resultSet.runStats?.recordCount || 0,
      executionSeconds: resultSet.runStats?.queryExecSeconds || 0,
      elapsedSeconds: resultSet.runStats?.elapsedSeconds || 0,
      bytes: resultSet.runStats?.bytes || 0,
    };
  }
}
