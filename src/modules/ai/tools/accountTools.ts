import { z } from "zod";
import { prisma } from "@/prisma/prisma";
import { prismaToOpenAPI } from "../prismaToOpenAPI";
import { TRPCError } from "@trpc/server";

/**
 * Account-related AI tools for the chat API
 */
export namespace accountTools {
  /**
   * Create multiple accounts in a single operation
   */
  export const createAccounts = {
    description:
      "Create one or more accounts in a single operation. Each account needs a name and type. Account types can be: WALLET (cryptocurrency wallet), BANK_ACCOUNT (traditional bank account), EXCHANGE_ACCOUNT (cryptocurrency exchange), or CREDIT_CARD (credit card account).",
    parameters: prismaToOpenAPI.createAccountsSchema,
    execute: async ({ accounts }: z.infer<typeof prismaToOpenAPI.createAccountsSchema>) => {
      try {
        // Validate input
        const validation = prismaToOpenAPI.validateCreateAccountsInput({ accounts });
        if (!validation.success) {
          return {
            success: false,
            error: "Invalid input parameters",
            details: validation.error.errors.map((err) => `${err.path.join(".")}: ${err.message}`).join(", "),
          };
        }

        const results = [];
        const errors = [];
        let successCount = 0;

        // Create accounts one by one to handle individual errors
        for (let i = 0; i < validation.data.accounts.length; i++) {
          const accountData = validation.data.accounts[i];

          try {
            const account = await prisma.account.create({
              data: accountData,
              include: {
                _count: {
                  select: {
                    transactions: true,
                  },
                },
              },
            });

            results.push({
              success: true,
              account: {
                id: account.id,
                name: account.name,
                type: account.type,
                createdAt: account.createdAt.toISOString(),
                transactionCount: account._count.transactions,
              },
            });
            successCount++;
          } catch (accountError) {
            console.error(`Failed to create account "${accountData.name}":`, accountError);

            let errorMessage = "Failed to create account";
            if (accountError instanceof Error) {
              if (accountError.message.includes("Unique constraint")) {
                errorMessage = `Account with name "${accountData.name}" already exists`;
              } else {
                errorMessage = accountError.message;
              }
            }

            results.push({
              success: false,
              error: errorMessage,
              accountData: { name: accountData.name, type: accountData.type },
            });
            errors.push(`${accountData.name}: ${errorMessage}`);
          }
        }

        // Return summary of results
        const totalRequested = validation.data.accounts.length;
        const failureCount = totalRequested - successCount;

        return {
          success: successCount > 0, // Success if at least one account was created
          message:
            successCount === totalRequested
              ? `Successfully created all ${successCount} accounts`
              : `Created ${successCount} of ${totalRequested} accounts${failureCount > 0 ? ` (${failureCount} failed)` : ""}`,
          summary: {
            totalRequested,
            successCount,
            failureCount,
            hasPartialSuccess: successCount > 0 && failureCount > 0,
          },
          results,
          ...(errors.length > 0 && { errors }),
        };
      } catch (error) {
        console.error("Failed to create accounts:", error);

        return {
          success: false,
          error: "Failed to create accounts",
          details: error instanceof Error ? error.message : "Unknown error occurred",
        };
      }
    },
  };

  /**
   * Create a single account (legacy support - wraps createAccounts)
   */
  export const createAccount = {
    description:
      "Create a single account with the specified name and type. Account types can be: WALLET (cryptocurrency wallet), BANK_ACCOUNT (traditional bank account), EXCHANGE_ACCOUNT (cryptocurrency exchange), or CREDIT_CARD (credit card account). For creating multiple accounts, use createAccounts instead.",
    parameters: prismaToOpenAPI.singleAccountSchema,
    execute: async (account: z.infer<typeof prismaToOpenAPI.singleAccountSchema>) => {
      // Delegate to createAccounts with a single account
      const result = await createAccounts.execute({ accounts: [account] });

      if (result.success && result.results && result.results.length > 0) {
        const accountResult = result.results[0];
        if (accountResult.success) {
          return {
            success: true,
            message: `Successfully created ${account.type.toLowerCase().replace("_", " ")} account "${account.name}"`,
            account: accountResult.account,
          };
        } else {
          return {
            success: false,
            error: accountResult.error || "Failed to create account",
            details: accountResult.error,
          };
        }
      }

      return {
        success: false,
        error: result.error || "Failed to create account",
        details: result.details || result.errors?.[0] || "Unknown error occurred",
      };
    },
  };

  /**
   * Get all accounts with basic information
   */
  export const getAccounts = {
    description: "Get a list of all accounts with their basic information including transaction counts",
    parameters: z.object({
      limit: z.number().min(1).max(100).default(20).describe("Maximum number of accounts to return"),
      type: z.enum(["WALLET", "BANK_ACCOUNT", "EXCHANGE_ACCOUNT", "CREDIT_CARD"]).optional().describe("Filter by account type"),
    }),
    execute: async ({ limit = 20, type }: { limit?: number; type?: string }) => {
      try {
        const whereClause = type ? { type: type as any } : {};

        const accounts = await prisma.account.findMany({
          where: whereClause,
          include: {
            _count: {
              select: {
                transactions: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: limit,
        });

        return {
          success: true,
          count: accounts.length,
          accounts: accounts.map((account) => ({
            id: account.id,
            name: account.name,
            type: account.type,
            createdAt: account.createdAt.toISOString().split("T")[0], // Just the date
            transactionCount: account._count.transactions,
          })),
        };
      } catch (error) {
        console.error("Failed to fetch accounts:", error);
        return {
          success: false,
          error: "Failed to fetch accounts",
          details: error instanceof Error ? error.message : "Unknown error occurred",
        };
      }
    },
  };

  /**
   * Get account statistics
   */
  export const getAccountStats = {
    description: "Get statistics about accounts including total count by type",
    parameters: z.object({}),
    execute: async () => {
      try {
        const [totalCount, accountsByType] = await Promise.all([
          prisma.account.count(),
          prisma.account.groupBy({
            by: ["type"],
            _count: {
              id: true,
            },
          }),
        ]);

        const typeStats = accountsByType.reduce((acc, item) => {
          acc[item.type] = item._count.id;
          return acc;
        }, {} as Record<string, number>);

        return {
          success: true,
          totalAccounts: totalCount,
          accountsByType: typeStats,
          availableTypes: prismaToOpenAPI.getAccountTypes(),
        };
      } catch (error) {
        console.error("Failed to fetch account statistics:", error);
        return {
          success: false,
          error: "Failed to fetch account statistics",
          details: error instanceof Error ? error.message : "Unknown error occurred",
        };
      }
    },
  };

  /**
   * Get all account tools for the chat API
   */
  export function getAllAccountTools() {
    return {
      createAccount,
      createAccounts,
      getAccounts,
      getAccountStats,
    };
  }
}
