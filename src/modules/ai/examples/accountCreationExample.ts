/**
 * Example demonstrating how the AI agent can create accounts
 * This file shows the integration between Prisma models, OpenAPI schemas, and AI tools
 */

import { prismaToOpenAPI } from "../prismaToOpenAPI";
import { accountTools } from "../tools/accountTools";
import { AccountType } from "@/prisma/generated";

/**
 * Example: Get OpenAPI function definition for single account creation
 */
export function getAccountCreationFunctionDefinition() {
  const functionDef = prismaToOpenAPI.getCreateAccountFunction();

  console.log("Single Account Creation - OpenAPI Function Definition:");
  console.log(JSON.stringify(functionDef, null, 2));

  return functionDef;
}

/**
 * Example: Get OpenAPI function definition for multiple accounts creation
 */
export function getMultipleAccountsCreationFunctionDefinition() {
  const functionDef = prismaToOpenAPI.getCreateAccountsFunction();

  console.log("Multiple Accounts Creation - OpenAPI Function Definition:");
  console.log(JSON.stringify(functionDef, null, 2));

  return functionDef;
}

/**
 * Example: Validate account creation input
 */
export function validateAccountInput(input: unknown) {
  const validation = prismaToOpenAPI.validateCreateAccountInput(input);
  
  if (validation.success) {
    console.log("✅ Valid input:", validation.data);
    return { valid: true, data: validation.data };
  } else {
    console.log("❌ Invalid input:", validation.error.errors);
    return { valid: false, errors: validation.error.errors };
  }
}

/**
 * Example: Simulate AI agent creating a single account
 */
export async function simulateAIAccountCreation() {
  console.log("🤖 AI Agent: Creating a new wallet account...");

  // This would typically come from the AI model's function call
  const aiInput = {
    name: "My Bitcoin Wallet",
    type: AccountType.WALLET
  };

  try {
    const result = await accountTools.createAccount.execute(aiInput);

    if (result.success) {
      console.log("✅ Account created successfully!");
      console.log("Account details:", result.account);
      console.log("Message:", result.message);
    } else {
      console.log("❌ Failed to create account:");
      console.log("Error:", result.error);
      console.log("Details:", result.details);
    }

    return result;
  } catch (error) {
    console.error("Unexpected error:", error);
    throw error;
  }
}

/**
 * Example: Simulate AI agent creating multiple accounts
 */
export async function simulateAIMultipleAccountsCreation() {
  console.log("🤖 AI Agent: Creating multiple accounts...");

  // This would typically come from the AI model's function call
  const aiInput = {
    accounts: [
      { name: "My Bitcoin Wallet", type: AccountType.WALLET },
      { name: "Main Checking Account", type: AccountType.BANK_ACCOUNT },
      { name: "Binance Trading", type: AccountType.EXCHANGE_ACCOUNT },
      { name: "Business Credit Card", type: AccountType.CREDIT_CARD }
    ]
  };

  try {
    const result = await accountTools.createAccounts.execute(aiInput);

    if (result.success) {
      console.log("✅ Accounts creation completed!");
      console.log("Message:", result.message);
      console.log("Summary:", result.summary);

      if (result.results) {
        console.log("\nAccount Details:");
        result.results.forEach((accountResult, index) => {
          if (accountResult.success) {
            console.log(`  ${index + 1}. ✅ ${accountResult.account?.name} (${accountResult.account?.type})`);
          } else {
            console.log(`  ${index + 1}. ❌ ${accountResult.accountData?.name} - ${accountResult.error}`);
          }
        });
      }

      if (result.errors && result.errors.length > 0) {
        console.log("\nErrors encountered:");
        result.errors.forEach(error => console.log(`  - ${error}`));
      }
    } else {
      console.log("❌ Failed to create accounts:");
      console.log("Error:", result.error);
      console.log("Details:", result.details);
    }

    return result;
  } catch (error) {
    console.error("Unexpected error:", error);
    throw error;
  }
}

/**
 * Example: Get all available account types with descriptions
 */
export function showAvailableAccountTypes() {
  const accountTypes = prismaToOpenAPI.getAccountTypes();
  
  console.log("Available Account Types:");
  accountTypes.forEach(type => {
    console.log(`- ${type.value}: ${type.description}`);
  });
  
  return accountTypes;
}

/**
 * Example: Simulate AI agent getting account statistics
 */
export async function simulateAIAccountStats() {
  console.log("🤖 AI Agent: Getting account statistics...");
  
  try {
    const result = await accountTools.getAccountStats.execute();
    
    if (result.success) {
      console.log("📊 Account Statistics:");
      console.log(`Total accounts: ${result.totalAccounts}`);
      console.log("Accounts by type:", result.accountsByType);
    } else {
      console.log("❌ Failed to get statistics:", result.error);
    }
    
    return result;
  } catch (error) {
    console.error("Unexpected error:", error);
    throw error;
  }
}

/**
 * Example: Complete workflow demonstration
 */
export async function demonstrateCompleteWorkflow() {
  console.log("🚀 Demonstrating AI Agent Account Management Workflow\n");
  
  // 1. Show available account types
  console.log("1. Available Account Types:");
  showAvailableAccountTypes();
  console.log();
  
  // 2. Show OpenAPI function definition
  console.log("2. OpenAPI Function Definition:");
  getAccountCreationFunctionDefinition();
  console.log();
  
  // 3. Validate different inputs
  console.log("3. Input Validation Examples:");
  
  // Valid input
  validateAccountInput({
    name: "Test Wallet",
    type: AccountType.WALLET
  });
  
  // Invalid input - empty name
  validateAccountInput({
    name: "",
    type: AccountType.WALLET
  });
  
  // Invalid input - wrong type
  validateAccountInput({
    name: "Test Account",
    type: "INVALID_TYPE"
  });
  console.log();
  
  // 4. Simulate account creation
  console.log("4. Account Creation:");
  await simulateAIAccountCreation();
  console.log();
  
  // 5. Get account statistics
  console.log("5. Account Statistics:");
  await simulateAIAccountStats();
  console.log();
  
  console.log("✅ Workflow demonstration complete!");
}

// Example usage in a chat context
export const exampleChatInteractions = [
  {
    userMessage: "Create a new wallet account called 'My Bitcoin Wallet'",
    aiFunction: "createAccount",
    aiParameters: {
      name: "My Bitcoin Wallet",
      type: "WALLET"
    }
  },
  {
    userMessage: "Add a bank account named 'Main Checking'",
    aiFunction: "createAccount",
    aiParameters: {
      name: "Main Checking",
      type: "BANK_ACCOUNT"
    }
  },
  {
    userMessage: "Create multiple accounts: a wallet called 'Trading Wallet', a bank account 'Savings Account', and an exchange account 'Coinbase Pro'",
    aiFunction: "createAccounts",
    aiParameters: {
      accounts: [
        { name: "Trading Wallet", type: "WALLET" },
        { name: "Savings Account", type: "BANK_ACCOUNT" },
        { name: "Coinbase Pro", type: "EXCHANGE_ACCOUNT" }
      ]
    }
  },
  {
    userMessage: "Set up my complete account structure: main wallet, checking account, savings account, trading exchange, and business credit card",
    aiFunction: "createAccounts",
    aiParameters: {
      accounts: [
        { name: "Main Wallet", type: "WALLET" },
        { name: "Checking Account", type: "BANK_ACCOUNT" },
        { name: "Savings Account", type: "BANK_ACCOUNT" },
        { name: "Trading Exchange", type: "EXCHANGE_ACCOUNT" },
        { name: "Business Credit Card", type: "CREDIT_CARD" }
      ]
    }
  },
  {
    userMessage: "Show me all my accounts",
    aiFunction: "getAccounts",
    aiParameters: {
      limit: 20
    }
  },
  {
    userMessage: "How many accounts do I have?",
    aiFunction: "getAccountStats",
    aiParameters: {}
  }
];

// Export for testing and demonstration
export const examples = {
  getAccountCreationFunctionDefinition,
  validateAccountInput,
  simulateAIAccountCreation,
  showAvailableAccountTypes,
  simulateAIAccountStats,
  demonstrateCompleteWorkflow,
  exampleChatInteractions
};
