import React, { useState, useEffect } from "react";
import { trpc } from "@/utils/trpc";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, Activity, Wallet, TrendingUp, ArrowUpDown, ExternalLink } from "lucide-react";
import { TransactionDetailsDrawer } from "@/components/TransactionDetailsDrawer";
import { currency } from "@/modules/core/currency";
import { SearchInput } from "@/components/SearchInput";
import { TransactionTableHeader, SortConfig } from "@/components/TransactionTableHeader";
import { DateRange } from "@/components/ui/date-range-picker";
import { AmountRange } from "@/components/ui/amount-range-filter";
import { useRouter } from "next/router";
import { DashboardLayout } from "@/components/DashboardLayout";
import { AccountType } from "@/prisma/generated";
import { CopyablePublicKey } from "@/components/AccountDataTable";

export default function TransactionsPage() {
  const [limit, setLimit] = useState(20);
  const [cursor, setCursor] = useState<string | undefined>(undefined);
  const [cursors, setCursors] = useState<string[]>([]);
  const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedType, setSelectedType] = useState<string | undefined>(undefined);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [amountRange, setAmountRange] = useState<AmountRange | undefined>(undefined);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: "executedAt", direction: "desc" });

  // Fetch transaction stats
  const stats = trpc.transactions.getStats.useQuery();

  // Fetch filtered stats when filters are applied
  const hasFilters =
    selectedAccountIds.length > 0 || !!searchTerm.trim() || !!selectedType || !!dateRange?.from || !!dateRange?.to || !!amountRange?.min || !!amountRange?.max;
  const filteredStats = trpc.transactions.getFilteredStats.useQuery(
    {
      ...(selectedAccountIds.length > 0 && { accountIds: selectedAccountIds }),
      ...(searchTerm.trim() && { search: searchTerm.trim() }),
      ...(selectedType && { type: selectedType as "transfer" | "trade" | "crypto_expense" }),
      ...(dateRange?.from && { dateFrom: dateRange.from.toISOString() }),
      ...(dateRange?.to && { dateTo: dateRange.to.toISOString() }),
      ...(amountRange?.min !== undefined && { amountMin: amountRange.min }),
      ...(amountRange?.max !== undefined && { amountMax: amountRange.max }),
    },
    {
      enabled: hasFilters, // Only fetch when filters are applied
    }
  );

  // Fetch accounts for filter
  const accounts = trpc.accounts.getAll.useQuery();

  // Fetch transactions with pagination
  const transactions = trpc.transactions.getAll.useQuery({
    limit,
    ...(cursor && { cursor }),
    ...(selectedAccountIds.length > 0 && { accountIds: selectedAccountIds }),
    ...(searchTerm.trim() && { search: searchTerm.trim() }),
    ...(selectedType && { type: selectedType as "transfer" | "trade" | "crypto_expense" }),
    ...(dateRange?.from && { dateFrom: dateRange.from.toISOString() }),
    ...(dateRange?.to && { dateTo: dateRange.to.toISOString() }),
    ...(amountRange?.min !== undefined && { amountMin: amountRange.min }),
    ...(amountRange?.max !== undefined && { amountMax: amountRange.max }),
    ...(sortConfig.direction && {
      sortField: sortConfig.field as "executedAt" | "account" | "type" | "amount",
      sortDirection: sortConfig.direction,
    }),
  });

  // Router for URL query handling
  const router = useRouter();

  // Handle transactionId from URL query parameters
  useEffect(() => {
    if (router.query.transactionId && typeof router.query.transactionId === "string") {
      setSelectedTransactionId(router.query.transactionId);
      setDrawerOpen(true);
    }
  }, [router.query.transactionId]);

  // Reset pagination when any filter changes
  useEffect(() => {
    setCursor(undefined);
    setCursors([]);
  }, [searchTerm, selectedAccountIds, selectedType, dateRange, amountRange, sortConfig]);

  const handleNextPage = () => {
    if (transactions.data?.nextCursor) {
      setCursors((prev) => [...prev, cursor || ""]);
      setCursor(transactions.data.nextCursor);
    }
  };

  const handlePreviousPage = () => {
    if (cursors.length > 0) {
      const newCursors = [...cursors];
      const previousCursor = newCursors.pop();
      setCursors(newCursors);
      setCursor(previousCursor === "" ? undefined : previousCursor);
    }
  };

  const handleTransactionClick = (transactionId: string) => {
    setSelectedTransactionId(transactionId);
    setDrawerOpen(true);
    router.push(`/transactions?transactionId=${transactionId}`, undefined, { shallow: true });
  };

  const handleDrawerOpenChange = (open: boolean) => {
    setDrawerOpen(open);
    if (!open) {
      setSelectedTransactionId(null);
      router.push("/transactions", undefined, { shallow: true });
    }
  };

  // Navigation between transactions
  const currentIndex = transactions.data?.transactions?.findIndex((t) => t.id === selectedTransactionId) ?? -1;
  const hasNext = currentIndex >= 0 && currentIndex < (transactions.data?.transactions?.length ?? 0) - 1;
  const hasPrevious = currentIndex > 0;

  const handleNavigateNext = () => {
    if (hasNext && transactions.data?.transactions) {
      const nextTransaction = transactions.data.transactions[currentIndex + 1];
      handleTransactionClick(nextTransaction.id);
    }
  };

  const handleNavigatePrevious = () => {
    if (hasPrevious && transactions.data?.transactions) {
      const previousTransaction = transactions.data.transactions[currentIndex - 1];
      handleTransactionClick(previousTransaction.id);
    }
  };

  const getTransactionTypeBadge = (transaction: any) => {
    if (transaction.transfer) {
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          Transfer
        </Badge>
      );
    }
    if (transaction.trade) {
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          Trade
        </Badge>
      );
    }
    if (transaction.cryptoExpense) {
      // Show specific crypto expense type
      const expenseType = transaction.cryptoExpense.type;
      const typeLabels = {
        BUYBACK: "Buyback",
        FLOOR_SWEEP: "Floor Sweep",
        LIQUIDITY_POOL: "LP Action",
        OTHER: "Crypto Expense",
      };
      const label = typeLabels[expenseType as keyof typeof typeLabels] || "Crypto Expense";

      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          {label}
        </Badge>
      );
    }
    return <Badge variant="outline">Unknown</Badge>;
  };

  const getAccountTypeLabel = (type: AccountType) => {
    switch (type) {
      case "WALLET":
        return "Wallet";
      case "BANK_ACCOUNT":
        return "Bank Account";
      case "EXCHANGE_ACCOUNT":
        return "Exchange Account";
      case "CREDIT_CARD":
        return "Credit Card";
      default:
        return type;
    }
  };

  const formatTransactionAmount = (transaction: any) => {
    if (transaction.transfer) {
      return currency.formatMonetary(transaction.transfer.amount, transaction.transfer.currencyCode);
    }
    if (transaction.trade) {
      return `${currency.formatMonetary(transaction.trade.amountFrom, transaction.trade.tokenFrom)} → ${currency.formatMonetary(
        transaction.trade.amountTo,
        transaction.trade.tokenTo
      )}`;
    }
    if (transaction.cryptoExpense) {
      // Show the main amount field as EUR equivalent
      const usdAmount = Number(transaction.cryptoExpense.amountUsd);
      return currency.formatMonetary(usdAmount, "EUR");
    }
    return "N/A";
  };

  const getTransactionDescription = (transaction: any) => {
    if (transaction.transfer) {
      return transaction.transfer.description || transaction.transfer.counterparty || "Transfer";
    }
    if (transaction.trade) {
      return transaction.trade.description || `${transaction.trade.tokenFrom} → ${transaction.trade.tokenTo}`;
    }
    if (transaction.cryptoExpense) {
      const expense = transaction.cryptoExpense;
      if (expense.type === "FLOOR_SWEEP" && expense.floorSweep) {
        return `${expense.type}: ${expense.floorSweep.collectionName || expense.floorSweep.collectionId}`;
      }
      if (expense.type === "LIQUIDITY_POOL" && expense.liquidityPool) {
        return `${expense.type}: ${expense.liquidityPool.action} (${expense.liquidityPool.tokenASymbol}/${expense.liquidityPool.tokenBSymbol})`;
      }
      if (expense.type === "BUYBACK" && expense.buyback) {
        return `${expense.type}: ${expense.buyback.swapFromSymbol} → ${expense.buyback.swapToSymbol}`;
      }
      return expense.type;
    }
    return "Unknown transaction";
  };

  return (
    <DashboardLayout title="Transactions">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Transactions</h1>
            <p className="text-muted-foreground">View and manage all transactions across accounts</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.total || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transfers</CardTitle>
              <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withTransfers || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trades</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withTrades || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Crypto Expenses</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withCryptoExpenses || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Other Actions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withLiquidityActions || 0}</div>
            </CardContent>
          </Card>
        </div>

        {/* Pagination and Items per page Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              {transactions.data?.transactions ? `Showing ${transactions.data.transactions.length} transactions` : "Loading..."}
            </div>
            <div className="w-32">
              <Select value={limit.toString()} onValueChange={(value) => setLimit(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="20">20 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                  <SelectItem value="100">100 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handlePreviousPage} disabled={cursors.length === 0}>
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button variant="outline" size="sm" onClick={handleNextPage} disabled={!transactions.data?.nextCursor}>
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Transactions Data Table */}
        <Card>
          <CardContent>
            <Table>
              <TableHeader>
                <TransactionTableHeader
                  dateRange={dateRange}
                  onDateRangeChange={setDateRange}
                  amountRange={amountRange}
                  onAmountRangeChange={setAmountRange}
                  accounts={accounts.data?.items || []}
                  selectedAccountIds={selectedAccountIds}
                  onAccountChange={setSelectedAccountIds}
                  selectedType={selectedType}
                  onTypeChange={setSelectedType}
                  searchTerm={searchTerm}
                  onSearchChange={setSearchTerm}
                  sortConfig={sortConfig}
                  onSortChange={setSortConfig}
                  getAccountTypeLabel={(type: string) => getAccountTypeLabel(type as AccountType)}
                />
              </TableHeader>
              {transactions.isLoading ? (
                <TableBody>
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="text-muted-foreground">Loading transactions...</div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              ) : transactions.data ? (
                <>
                  {transactions.data.transactions?.length === 0 ? (
                    <TableBody>
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="text-muted-foreground">No transactions found</div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  ) : (
                    <TableBody>
                      {transactions.data?.transactions?.map((transaction) => (
                        <TableRow key={transaction.id} className="cursor-pointer hover:bg-muted/50" onClick={() => handleTransactionClick(transaction.id)}>
                          <TableCell className="text-sm text-muted-foreground">{transaction.executedAt.toLocaleDateString("de-DE")}</TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate text-sm">
                              <div className="font-medium">{transaction.account?.name || "Unknown Account"}</div>
                              <div className="text-xs text-muted-foreground">
                                {transaction.account.type === "WALLET" && transaction.account.publicKey ? (
                                  <CopyablePublicKey publicKey={transaction.account.publicKey} />
                                ) : (
                                  getAccountTypeLabel(transaction.account?.type || "WALLET")
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{getTransactionTypeBadge(transaction)}</TableCell>
                          <TableCell className="text-sm">
                            <div className="font-medium">{formatTransactionAmount(transaction)}</div>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate text-sm">{getTransactionDescription(transaction)}</div>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {transaction.transactionGroupId ? (
                              <Badge variant="outline" className="text-xs">
                                Group
                              </Badge>
                            ) : (
                              <span className="text-xs">Individual</span>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {transaction.solanaTransactionId && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.open(`https://solscan.io/tx/${transaction.solanaTransactionId}`, "_blank");
                                }}
                                className="h-8 w-8 p-0 hover:bg-muted"
                                title="View on Solscan"
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  )}
                </>
              ) : (
                <TableBody>
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="text-destructive">Failed to load transactions: {transactions.error?.message}</div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              )}
            </Table>
          </CardContent>
        </Card>

        {/* Transaction Details Drawer */}
        <TransactionDetailsDrawer
          transactionId={selectedTransactionId}
          open={drawerOpen}
          onOpenChange={handleDrawerOpenChange}
          onNavigateNext={handleNavigateNext}
          onNavigatePrevious={handleNavigatePrevious}
          hasNext={hasNext}
          hasPrevious={hasPrevious}
        />
      </div>
    </DashboardLayout>
  );
}
