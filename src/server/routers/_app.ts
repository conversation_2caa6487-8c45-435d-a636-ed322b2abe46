import { createTRPCRouter } from "@/server/trpc";
import { exampleRouter } from "./example";
import { invoiceRouter } from "@/modules/invoices/invoiceRouter";
import { vendorRouter } from "@/modules/vendors/vendorRouter";
import { settingsRouter } from "@/modules/settings/settingsRouter";
import { flipsideRouter } from "@/modules/flipside/flipsideRouter";
import { accountRouter } from "@/modules/accounts/accountRouter";
import { transactionRouter } from "@/modules/transactions/transactionRouter";
import { csvRouter } from "@/modules/csv/csvRouter";
import { reconciliationRouter } from "@/modules/reconciliation/reconciliationRouter";
import { cryptoExpenseRouter } from "@/modules/crypto-expenses/cryptoExpenseRouter";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  example: exampleRouter,
  invoices: invoiceRouter,
  vendors: vendorRouter,
  settings: settingsRouter,
  flipside: flipsideRouter,
  accounts: accountRouter,
  transactions: transactionRouter,
  csv: csvRouter,
  reconciliation: reconciliationRouter,
  cryptoExpenses: cryptoExpenseRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
