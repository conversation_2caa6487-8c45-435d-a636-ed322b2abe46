import React from "react";
import { trpc } from "@/utils/trpc";
import { useTrpcErrorHandler } from "@/hooks/useTrpcErrorHandler";
import { ErrorDisplay, QueryErrorDisplay, MutationErrorDisplay, LoadingError } from "@/components/ui/error-display";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

/**
 * Example component demonstrating beautiful error handling patterns for tRPC
 */
export function ErrorHandlingExample() {
  const { handleError } = useTrpcErrorHandler();

  // Example query with error handling
  const exampleQuery = trpc.example.getAll.useQuery(undefined, {
    onError: (error) => {
      // Custom error handling for this specific query
      handleError(error, "Failed to load example data");
    },
  });

  // Example mutation with error handling
  const exampleMutation = trpc.example.create.useMutation({
    onSuccess: () => {
      toast.success("Example created successfully!");
      exampleQuery.refetch();
    },
    onError: (error) => {
      // Custom error handling for this specific mutation
      handleError(error, "Failed to create example");
    },
  });

  // Mutation with inline error display
  const createMutationWithInlineError = trpc.example.create.useMutation({
    onSuccess: () => {
      toast.success("Created successfully!");
    },
    // Note: We're not using onError here to demonstrate inline error display
  });

  const handleCreateExample = async () => {
    try {
      await exampleMutation.mutateAsync({
        name: "Test Example",
        description: "This is a test",
      });
    } catch (error) {
      // Error is already handled by the mutation's onError
      console.log("Error caught in component:", error);
    }
  };

  const handleCreateWithInlineError = async () => {
    try {
      await createMutationWithInlineError.mutateAsync({
        name: "Test Example",
        description: "This will show inline error",
      });
    } catch (error) {
      // Error will be displayed inline below
    }
  };

  const simulateError = () => {
    // Simulate a client-side error for demonstration
    const fakeError = new Error("This is a simulated error for demonstration");
    handleError(fakeError as any, "Simulated error occurred");
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>tRPC Error Handling Examples</CardTitle>
          <CardDescription>
            Demonstrating beautiful error messages for tRPC queries and mutations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          
          {/* Example 1: Query with automatic error handling */}
          <div>
            <h3 className="text-lg font-semibold mb-2">1. Query with Automatic Error Handling</h3>
            <p className="text-sm text-muted-foreground mb-2">
              Errors are automatically shown as toast notifications
            </p>
            {exampleQuery.isLoading && <div>Loading...</div>}
            {exampleQuery.error && (
              <QueryErrorDisplay 
                error={exampleQuery.error} 
                refetch={exampleQuery.refetch}
              />
            )}
            {exampleQuery.data && (
              <div className="p-2 bg-green-50 rounded">
                Data loaded successfully: {JSON.stringify(exampleQuery.data)}
              </div>
            )}
          </div>

          {/* Example 2: Mutation with toast error handling */}
          <div>
            <h3 className="text-lg font-semibold mb-2">2. Mutation with Toast Error Handling</h3>
            <p className="text-sm text-muted-foreground mb-2">
              Errors are shown as toast notifications
            </p>
            <Button 
              onClick={handleCreateExample}
              disabled={exampleMutation.isPending}
            >
              {exampleMutation.isPending ? "Creating..." : "Create Example (Toast Error)"}
            </Button>
          </div>

          {/* Example 3: Mutation with inline error display */}
          <div>
            <h3 className="text-lg font-semibold mb-2">3. Mutation with Inline Error Display</h3>
            <p className="text-sm text-muted-foreground mb-2">
              Errors are shown inline below the button
            </p>
            <div className="space-y-2">
              <Button 
                onClick={handleCreateWithInlineError}
                disabled={createMutationWithInlineError.isPending}
              >
                {createMutationWithInlineError.isPending ? "Creating..." : "Create Example (Inline Error)"}
              </Button>
              {createMutationWithInlineError.error && (
                <MutationErrorDisplay 
                  error={createMutationWithInlineError.error}
                  retry={() => createMutationWithInlineError.reset()}
                />
              )}
            </div>
          </div>

          {/* Example 4: Different error display sizes */}
          <div>
            <h3 className="text-lg font-semibold mb-2">4. Different Error Display Sizes</h3>
            <div className="space-y-2">
              <ErrorDisplay 
                error="Small error message" 
                size="sm" 
                title="Small Error"
              />
              <ErrorDisplay 
                error="Medium error message with more details" 
                size="md" 
                title="Medium Error"
                showRetry
                onRetry={() => console.log("Retry clicked")}
              />
              <ErrorDisplay 
                error="Large error message with comprehensive details and description" 
                size="lg" 
                title="Large Error"
                showRetry
                onRetry={() => console.log("Retry clicked")}
              />
            </div>
          </div>

          {/* Example 5: Loading error component */}
          <div>
            <h3 className="text-lg font-semibold mb-2">5. Loading Error Component</h3>
            <LoadingError 
              message="Failed to load the requested data"
              onRetry={() => console.log("Retry loading")}
            />
          </div>

          {/* Example 6: Simulate error */}
          <div>
            <h3 className="text-lg font-semibold mb-2">6. Simulate Error</h3>
            <p className="text-sm text-muted-foreground mb-2">
              Click to see a simulated error toast
            </p>
            <Button onClick={simulateError} variant="destructive">
              Simulate Error
            </Button>
          </div>

        </CardContent>
      </Card>
    </div>
  );
}
