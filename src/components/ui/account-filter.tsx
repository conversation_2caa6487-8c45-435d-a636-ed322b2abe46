import * as React from "react";
import { Check, ChevronDown, Search, X } from "lucide-react";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

export interface Account {
  id: string;
  name: string;
  type: string;
}

export interface Vendor {
  id: string;
  name: string;
}

interface AccountFilterProps {
  accounts: Account[];
  selectedAccountId?: string;
  onAccountChange?: (accountId: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  getAccountTypeLabel?: (type: string) => string;
}

interface MultiAccountFilterProps {
  accounts: Account[];
  selectedAccountIds?: string[];
  onAccountChange?: (accountIds: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  getAccountTypeLabel?: (type: string) => string;
}

// Account type categories for better organization
const getAccountTypeCategory = (type: string) => {
  switch (type) {
    case "BANK_ACCOUNT":
      return "Bank";
    case "CREDIT_CARD":
      return "Cards";
    case "WALLET":
      return "Wallets";
    case "EXCHANGE_ACCOUNT":
      return "Exchange";
    default:
      return "Other";
  }
};

// Group accounts by category
const groupAccountsByCategory = (accounts: Account[], getAccountTypeLabel: (type: string) => string) => {
  const grouped = accounts.reduce((acc, account) => {
    const category = getAccountTypeCategory(account.type);
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(account);
    return acc;
  }, {} as Record<string, Account[]>);

  // Sort accounts within each category by name
  Object.keys(grouped).forEach((category) => {
    grouped[category].sort((a, b) => a.name.localeCompare(b.name));
  });

  return grouped;
};

export function AccountFilter({
  accounts,
  selectedAccountId,
  onAccountChange,
  placeholder = "Select account",
  disabled = false,
  className,
  getAccountTypeLabel = (type) => type,
}: AccountFilterProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedAccount = accounts.find((account) => account.id === selectedAccountId);

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAccountChange?.(undefined);
  };

  const handleSelect = (accountId: string) => {
    if (accountId === selectedAccountId) {
      onAccountChange?.(undefined);
    } else {
      onAccountChange?.(accountId);
    }
    setIsOpen(false);
  };

  const filteredAccounts = accounts.filter(
    (account) =>
      account.name.toLowerCase().includes(searchValue.toLowerCase()) || getAccountTypeLabel(account.type).toLowerCase().includes(searchValue.toLowerCase())
  );

  const displayText = selectedAccount ? `${selectedAccount.name} (${getAccountTypeLabel(selectedAccount.type)})` : placeholder;

  const groupedAccounts = groupAccountsByCategory(filteredAccounts, getAccountTypeLabel);
  const categoryOrder = ["Bank", "Cards", "Wallets", "Exchange", "Other"];

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn("w-full justify-between font-normal", !selectedAccount && "text-muted-foreground", className)}
          disabled={disabled}
        >
          <span className="flex-1 truncate text-left">{displayText}</span>
          <div className="flex items-center gap-1">
            {selectedAccount && <X className="h-4 w-4 hover:text-destructive" onClick={handleClear} />}
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="p-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search accounts..." value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className="pl-8" />
          </div>
        </div>
        <ScrollArea className="max-h-60">
          <div className="p-1">
            {filteredAccounts.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">No accounts found.</div>
            ) : (
              categoryOrder.map((category) => {
                const categoryAccounts = groupedAccounts[category];
                if (!categoryAccounts || categoryAccounts.length === 0) return null;

                return (
                  <div key={category} className="mb-2">
                    <div className="px-2 py-1 text-xs font-semibold text-muted-foreground uppercase tracking-wider">{category}</div>
                    {categoryAccounts.map((account) => (
                      <div
                        key={account.id}
                        className={cn(
                          "flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground ml-2",
                          selectedAccountId === account.id && "bg-accent"
                        )}
                        onClick={() => handleSelect(account.id)}
                      >
                        <Check className={cn("h-4 w-4", selectedAccountId === account.id ? "opacity-100" : "opacity-0")} />
                        <div className="flex-1">
                          <div className="font-medium">{account.name}</div>
                          <div className="text-xs text-muted-foreground">{getAccountTypeLabel(account.type)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}

export function MultiAccountFilter({
  accounts,
  selectedAccountIds = [],
  onAccountChange,
  placeholder = "Select accounts",
  disabled = false,
  className,
  getAccountTypeLabel = (type) => type,
}: MultiAccountFilterProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedAccounts = accounts.filter((account) => selectedAccountIds.includes(account.id));

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAccountChange?.([]);
  };

  const handleSelect = (accountId: string) => {
    const newSelectedIds = selectedAccountIds.includes(accountId) ? selectedAccountIds.filter((id) => id !== accountId) : [...selectedAccountIds, accountId];

    onAccountChange?.(newSelectedIds);
  };

  const filteredAccounts = accounts.filter(
    (account) =>
      account.name.toLowerCase().includes(searchValue.toLowerCase()) || getAccountTypeLabel(account.type).toLowerCase().includes(searchValue.toLowerCase())
  );

  const displayText =
    selectedAccounts.length === 0
      ? placeholder
      : selectedAccounts.length === 1
      ? `${selectedAccounts[0].name} (${getAccountTypeLabel(selectedAccounts[0].type)})`
      : `${selectedAccounts.length} accounts selected`;

  const groupedAccounts = groupAccountsByCategory(filteredAccounts, getAccountTypeLabel);
  const categoryOrder = ["Bank", "Cards", "Wallets", "Exchange", "Other"];

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn("w-full justify-between font-normal", selectedAccounts.length === 0 && "text-muted-foreground", className)}
          disabled={disabled}
        >
          <div className="flex-1 flex items-center gap-2 overflow-hidden">
            <span className="truncate">{displayText}</span>
            {selectedAccounts.length > 1 && (
              <Badge variant="secondary" className="text-xs">
                {selectedAccounts.length}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1">
            {selectedAccounts.length > 0 && <X className="h-4 w-4 hover:text-destructive" onClick={handleClear} />}
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="p-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search accounts..." value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className="pl-8" />
          </div>
        </div>
        {selectedAccounts.length > 0 && (
          <div className="px-2 pb-2">
            <div className="text-xs text-muted-foreground mb-1">Selected accounts:</div>
            <div className="flex flex-wrap gap-1">
              {selectedAccounts.map((account) => (
                <Badge key={account.id} variant="secondary" className="text-xs">
                  {account.name}
                  <X
                    className="h-3 w-3 ml-1 hover:text-destructive cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelect(account.id);
                    }}
                  />
                </Badge>
              ))}
            </div>
          </div>
        )}
        <ScrollArea className="max-h-60">
          <div className="p-1">
            {filteredAccounts.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">No accounts found.</div>
            ) : (
              categoryOrder.map((category) => {
                const categoryAccounts = groupedAccounts[category];
                if (!categoryAccounts || categoryAccounts.length === 0) return null;

                return (
                  <div key={category} className="mb-2">
                    <div className="px-2 py-1 text-xs font-semibold text-muted-foreground uppercase tracking-wider">{category}</div>
                    {categoryAccounts.map((account) => (
                      <div
                        key={account.id}
                        className={cn(
                          "flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground ml-2",
                          selectedAccountIds.includes(account.id) && "bg-accent"
                        )}
                        onClick={() => handleSelect(account.id)}
                      >
                        <Check className={cn("h-4 w-4", selectedAccountIds.includes(account.id) ? "opacity-100" : "opacity-0")} />
                        <div className="flex-1">
                          <div className="font-medium">{account.name}</div>
                          <div className="text-xs text-muted-foreground">{getAccountTypeLabel(account.type)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}

interface VendorFilterProps {
  vendors: Vendor[];
  selectedVendorId?: string;
  onVendorChange?: (vendorId: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function VendorFilter({ vendors, selectedVendorId, onVendorChange, placeholder = "Select vendor", disabled = false, className }: VendorFilterProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedVendor = vendors.find((vendor) => vendor.id === selectedVendorId);

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onVendorChange?.(undefined);
  };

  const handleSelect = (vendorId: string) => {
    if (vendorId === selectedVendorId) {
      onVendorChange?.(undefined);
    } else {
      onVendorChange?.(vendorId);
    }
    setIsOpen(false);
  };

  const filteredVendors = vendors.filter((vendor) => vendor.name.toLowerCase().includes(searchValue.toLowerCase()));

  const displayText = selectedVendor ? selectedVendor.name : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn("w-full justify-between font-normal", !selectedVendor && "text-muted-foreground", className)}
          disabled={disabled}
        >
          <div className="flex items-center justify-between w-full">
            <span className="truncate">{displayText}</span>
            {selectedVendor && <X className="h-4 w-4 shrink-0 opacity-50 hover:opacity-100 ml-2" onClick={handleClear} />}
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="p-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search vendors..." value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className="pl-8" />
          </div>
        </div>
        <ScrollArea className="max-h-60">
          <div className="p-1">
            {filteredVendors.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">No vendors found.</div>
            ) : (
              filteredVendors.map((vendor) => (
                <div
                  key={vendor.id}
                  className={cn(
                    "flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground",
                    selectedVendorId === vendor.id && "bg-accent"
                  )}
                  onClick={() => handleSelect(vendor.id)}
                >
                  <Check className={cn("h-4 w-4", selectedVendorId === vendor.id ? "opacity-100" : "opacity-0")} />
                  <div className="flex-1">
                    <div className="font-medium">{vendor.name}</div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
